// Route for language-specific pages
// This handles routes like /en, /pt, /es
export default function route(pageContext) {
  const { urlPathname } = pageContext;
  
  // Extract language from URL
  const segments = urlPathname.split('/').filter(Boolean);
  const lang = segments[0];
  
  // Valid languages
  const validLanguages = ['en', 'pt', 'es'];
  
  if (validLanguages.includes(lang)) {
    return {
      routeParams: { lang },
      // Remove language from the remaining path
      urlLogical: '/' + segments.slice(1).join('/')
    };
  }
  
  return false;
}
