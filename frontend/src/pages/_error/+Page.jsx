import NotFoundPage from '../NotFoundPage';

export default function ErrorPage(pageContext) {
  const { is404 } = pageContext;
  
  if (is404) {
    return <NotFoundPage />;
  }
  
  // For other errors, show a generic error page
  return (
    <div className="min-h-screen bg-[#0d0d0d] text-white flex items-center justify-center">
      <div className="text-center">
        <h1 className="text-4xl font-bold mb-4">Something went wrong</h1>
        <p className="text-gray-400 mb-8">We're sorry, but something unexpected happened.</p>
        <a 
          href="/" 
          className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors"
        >
          Go Home
        </a>
      </div>
    </div>
  );
}
