import React from 'react';
import PropTypes from 'prop-types';
import { AuthProvider } from '../context/AuthContext';
import { LanguageProvider } from '../context/LanguageContext';
import { SidebarProvider } from '../context/SidebarContext';
import { NotificationProvider } from '../context/NotificationContext';
import { GoogleOAuthProvider } from '@react-oauth/google';
import NotificationContainer from '../components/NotificationContainer';
import Header from '../components/Header';
import Footer from '../components/Footer';
import Sidebar from '../components/Sidebar';
import { useSidebar } from '../context/SidebarContext';
import { useLayoutConfig } from '../hooks/useLayoutConfig';

// Import CSS
import '../index.css';
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';

function AppContent({ children }) {
  const { isSidebarOpen, toggleSidebar } = useSidebar();
  const { shouldShowSidebar, shouldShowHeader, shouldShowFooter } = useLayoutConfig();

  // Admin routes get minimal layout (no header, no footer, no sidebar)
  if (!shouldShowHeader && !shouldShowFooter) {
    return (
      <div className="min-h-screen bg-[#0d0d0d] text-white">
        {children}
        <NotificationContainer />
      </div>
    );
  }

  // Regular layout - conditionally show sidebar, but always show header/footer for non-admin routes
  return (
    <div className="min-h-screen bg-[#0d0d0d] text-white">
      {shouldShowHeader && <Header />}
      <div className="flex flex-1">
        {shouldShowSidebar && <Sidebar isOpen={isSidebarOpen} onToggle={toggleSidebar} />}
        <main className={`flex-1 ${shouldShowSidebar && isSidebarOpen ? 'lg:pl-64' : 'lg:pl-0'} overflow-x-hidden`}>
          {children}
        </main>
      </div>
      {shouldShowFooter && <Footer />}
      <NotificationContainer />
    </div>
  );
}

AppContent.propTypes = {
  children: PropTypes.node.isRequired
};

export default function Layout({ children }) {
  return (
    <GoogleOAuthProvider clientId={import.meta.env.VITE_GOOGLE_CLIENT_ID}>
      <LanguageProvider>
        <AuthProvider>
          <SidebarProvider>
            <NotificationProvider>
              <div className="antialiased text-gray-900">
                <AppContent>
                  {children}
                </AppContent>
              </div>
            </NotificationProvider>
          </SidebarProvider>
        </AuthProvider>
      </LanguageProvider>
    </GoogleOAuthProvider>
  );
}

Layout.propTypes = {
  children: PropTypes.node.isRequired
};
