import React from 'react';
import { renderToString } from 'react-dom/server';
import { StaticRouter } from 'react-router-dom/server';
import Layout from './+Layout.jsx';
import { escapeInject, dangerouslySkipEscape } from 'vike/server';

export default async function onRenderServer(pageContext) {
  const { Page, pageProps, urlOriginal } = pageContext;
  
  const page = (
    <StaticRouter location={urlOriginal}>
      <Layout>
        <Page {...pageProps} />
      </Layout>
    </StaticRouter>
  );

  const pageHtml = renderToString(page);

  const documentHtml = escapeInject`<!DOCTYPE html>
    <html lang="en">
      <head>
        <meta charset="UTF-8" />
        <link rel="icon" type="image/svg+xml" href="/favicon.ico" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>${pageContext.title || 'IndieRepo - Discover Amazing Indie Games'}</title>
        <meta name="description" content="${pageContext.description || 'Discover and play amazing indie games from talented developers around the world.'}" />
        
        <!-- Favicon -->
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
        <link rel="manifest" href="/site.webmanifest">
        
        <!-- Open Graph / Facebook -->
        <meta property="og:type" content="website">
        <meta property="og:url" content="${urlOriginal}">
        <meta property="og:title" content="${pageContext.title || 'IndieRepo - Discover Amazing Indie Games'}">
        <meta property="og:description" content="${pageContext.description || 'Discover and play amazing indie games from talented developers around the world.'}">
        
        <!-- Twitter -->
        <meta property="twitter:card" content="summary_large_image">
        <meta property="twitter:url" content="${urlOriginal}">
        <meta property="twitter:title" content="${pageContext.title || 'IndieRepo - Discover Amazing Indie Games'}">
        <meta property="twitter:description" content="${pageContext.description || 'Discover and play amazing indie games from talented developers around the world.'}">
      </head>
      <body>
        <div id="root">${dangerouslySkipEscape(pageHtml)}</div>
      </body>
    </html>`;

  return {
    documentHtml,
    pageContext: {
      // We can add custom pageContext properties here
    }
  };
}
