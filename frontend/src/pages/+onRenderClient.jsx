import React from 'react';
import { hydrateRoot, createRoot } from 'react-dom/client';
import { <PERSON>rowserRouter } from 'react-router-dom';
import Layout from './+Layout.jsx';

export default async function onRenderClient(pageContext) {
  const { Page, pageProps } = pageContext;
  
  const page = (
    <BrowserRouter>
      <Layout>
        <Page {...pageProps} />
      </Layout>
    </BrowserRouter>
  );

  const container = document.getElementById('root');
  
  if (pageContext.isHydration) {
    // Hydrate the server-rendered content
    hydrateRoot(container, page);
  } else {
    // Client-side navigation
    const root = createRoot(container);
    root.render(page);
  }
}
