import vikeReact from 'vike-react/config'

export default {
  // Inherit from vike-react
  extends: vikeReact,
  
  // Global configuration
  title: 'IndieRepo - Discover Amazing Indie Games',
  description: 'Discover and play amazing indie games from talented developers around the world.',
  
  // Enable client-side routing for SPA-like navigation
  clientRouting: true,
  
  // Hydration configuration
  hydrationCanBeAborted: true,
  
  // Meta configuration
  meta: {
    title: {
      env: { server: true, client: true }
    },
    description: {
      env: { server: true, client: true }
    }
  }
}
