/**
 * Search utility functions for filtering and matching games
 */

/**
 * Check if text starts with search term (word boundary matching)
 * @param {string} text - Text to search in
 * @param {string} searchTerm - Term to search for
 * @returns {boolean} - True if text starts with search term or any word starts with search term
 */
export const startsWithTerm = (text, searchTerm) => {
  if (!text || !searchTerm) return false;
  
  const lowerText = text.toLowerCase();
  const lowerSearchTerm = searchTerm.toLowerCase();

  // Check if it starts with the search term
  if (lowerText.startsWith(lowerSearchTerm)) return true;

  // Check if any word starts with the search term
  const words = lowerText.split(/\s+/);
  return words.some(word => word.startsWith(lowerSearchTerm));
};

/**
 * Filter games based on strict word-boundary search
 * @param {Array} games - Array of games to filter
 * @param {string} searchTerm - Search term
 * @returns {Array} - Filtered and sorted games
 */
export const filterGamesBySearch = (games, searchTerm) => {
  if (!searchTerm.trim() || !games.length) return games;

  const filtered = games.filter(game => {
    // Only include games where title, genre, tags, or description STARTS with the search term
    const titleStartsMatch = startsWithTerm(game.title, searchTerm);

    // For very short search terms (1-2 characters), be more restrictive with genre matches
    const genreStartsMatch = searchTerm.length >= 3 ? startsWithTerm(game.genre, searchTerm) : false;

    // Handle tags properly - check each individual tag, not the whole tags string
    let tagsStartsMatch = false;
    if (game.tags && searchTerm.length >= 2) { // Only check tags for 2+ character searches
      if (Array.isArray(game.tags)) {
        // If tags is an array
        tagsStartsMatch = game.tags.some(tag => startsWithTerm(tag, searchTerm));
      } else if (typeof game.tags === 'string') {
        // If tags is a comma-separated string
        const tagArray = game.tags.split(',').map(tag => tag.trim());
        tagsStartsMatch = tagArray.some(tag => startsWithTerm(tag, searchTerm));
      }
    }

    // For very short search terms, be more restrictive with description matches
    const descStartsMatch = searchTerm.length >= 3 ? startsWithTerm(game.description, searchTerm) : false;

    return titleStartsMatch || genreStartsMatch || tagsStartsMatch || descStartsMatch;
  });

  // Sort results by relevance (title matches first, then genre, then tags, then description)
  return filtered.sort((a, b) => {
    const aStartsTitle = startsWithTerm(a.title, searchTerm);
    const bStartsTitle = startsWithTerm(b.title, searchTerm);
    const aStartsGenre = searchTerm.length >= 3 ? startsWithTerm(a.genre, searchTerm) : false;
    const bStartsGenre = searchTerm.length >= 3 ? startsWithTerm(b.genre, searchTerm) : false;

    // Handle tags properly for sorting
    const aStartsTags = (a.tags && searchTerm.length >= 2) ? (
      Array.isArray(a.tags)
        ? a.tags.some(tag => startsWithTerm(tag, searchTerm))
        : a.tags.split(',').map(tag => tag.trim()).some(tag => startsWithTerm(tag, searchTerm))
    ) : false;

    const bStartsTags = (b.tags && searchTerm.length >= 2) ? (
      Array.isArray(b.tags)
        ? b.tags.some(tag => startsWithTerm(tag, searchTerm))
        : b.tags.split(',').map(tag => tag.trim()).some(tag => startsWithTerm(tag, searchTerm))
    ) : false;

    // Priority order: Title > Genre > Tags > Description
    if (aStartsTitle && !bStartsTitle) return -1;
    if (!aStartsTitle && bStartsTitle) return 1;
    if (aStartsGenre && !bStartsGenre) return -1;
    if (!aStartsGenre && bStartsGenre) return 1;
    if (aStartsTags && !bStartsTags) return -1;
    if (!aStartsTags && bStartsTags) return 1;

    // If both have same priority, sort alphabetically
    return a.title.localeCompare(b.title);
  });
};
