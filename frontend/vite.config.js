import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';
import vike from 'vike/plugin';
import { fileURLToPath } from 'node:url';
import { dirname } from 'node:path';

const __dirname = dirname(fileURLToPath(import.meta.url));

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  // Load env file based on mode in the project root
  const env = loadEnv(mode, __dirname, '');
  
  const config = {
    plugins: [react()],
    
    // Environment variables configuration
    define: {
      // Ensure process.env is available in the client
      'process.env.NODE_ENV': JSON.stringify(mode)
    },
    
    // Build configuration
    build: {
      outDir: 'dist',
      sourcemap: mode === 'development',
      minify: mode === 'production',
      // Optimize dependencies
      rollupOptions: {
        output: {
          manualChunks: {
            vendor: ['react', 'react-dom'],
          },
        },
      },
    }
  };

  // Development-specific configuration
  if (mode === 'development') {
    config.server = {
      proxy: {
        '/api': {
          target: env.VITE_API_URL || 'http://localhost:3000',
          changeOrigin: true,
          secure: false,
        }
      },
      cors: {
        origin: '*',
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
        allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
      },
      host: '0.0.0.0',
      port: 5173,
      allowedHosts: ['.indierepo.com']
    };
  }

  return config;
});
